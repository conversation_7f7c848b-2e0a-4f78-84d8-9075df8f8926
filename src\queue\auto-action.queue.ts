import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { AutoMode } from '../user/enums/auto-mode.enum';
import { AutoActionJobData, AutoActionJobOptions } from './dto/auto-action-job.dto';

@Injectable()
export class AutoActionQueue {
  private readonly logger = new Logger(AutoActionQueue.name);

  // Production-ready constants
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY_BASE = 2000; // 2 seconds base delay
  private readonly REPEAT_INTERVAL = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_CONCURRENT_JOBS = 10; // Prevent queue overload
  private readonly CLEANUP_BATCH_SIZE = 100; // Process cleanup in batches
  private readonly OPERATION_TIMEOUT = 10000; // 10 seconds for queue operations

  constructor(
    @InjectQueue('auto-actions')
    private readonly autoActionQueue: Queue<AutoActionJobData>,
  ) {}

  /**
   * Generate a consistent job name
   */
  private generateJobName(userId: number, targetId: string, type: AutoMode): string {
    return `${userId}_${targetId}_${type}`;
  }

  /**
   * Add a new auto action job to the queue with comprehensive validation and error handling
   */
  async addAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<void> {
    // Input validation
    if (!userId || userId <= 0) {
      throw new Error('Invalid userId provided');
    }
    if (!targetId || typeof targetId !== 'string') {
      throw new Error('Invalid targetId provided');
    }
    if (!Object.values(AutoMode).includes(type)) {
      throw new Error(`Invalid auto mode type: ${type}`);
    }

    const jobName = this.generateJobName(userId, targetId, type);
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    // Production-ready job options with comprehensive configuration
    const jobOptions: AutoActionJobOptions = {
      repeat: {
        every: this.REPEAT_INTERVAL,
      },
      removeOnComplete: 10, // Keep last 10 successful jobs for debugging
      removeOnFail: 50, // Keep more failed jobs for analysis
      attempts: this.MAX_RETRIES,
      backoff: {
        type: 'exponential',
        delay: this.RETRY_DELAY_BASE,
      },
    };

    try {
      // Check if job already exists before adding
      const existingJob = await this.jobExists(userId, targetId, type);
      if (existingJob) {
        this.logger.warn(`Job already exists: ${jobName}, skipping creation`);
        return;
      }

      // Add job with timeout protection
      const addJobPromise = this.autoActionQueue.add(jobName, jobData, jobOptions);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Job creation timeout')), this.OPERATION_TIMEOUT)
      );

      await Promise.race([addJobPromise, timeoutPromise]);

      this.logger.log(`Successfully added auto action job: ${jobName} (interval: ${this.REPEAT_INTERVAL}ms)`);
    } catch (error) {
      this.logger.error(`Failed to add auto action job: ${jobName}`, {
        error: error.message,
        stack: error.stack,
        userId,
        targetId,
        type,
      });
      throw new Error(`Failed to create auto action job: ${error.message}`);
    }
  }

  /**
   * Add a one-time retry job with delay and comprehensive error handling
   */
  async addRetryJob(
    userId: number,
    targetId: string,
    type: AutoMode,
    delayMs: number,
  ): Promise<void> {
    // Input validation
    if (!userId || userId <= 0) {
      throw new Error('Invalid userId provided for retry job');
    }
    if (!targetId || typeof targetId !== 'string') {
      throw new Error('Invalid targetId provided for retry job');
    }
    if (!Object.values(AutoMode).includes(type)) {
      throw new Error(`Invalid auto mode type for retry job: ${type}`);
    }
    if (delayMs < 0 || delayMs > 24 * 60 * 60 * 1000) { // Max 24 hours delay
      throw new Error(`Invalid delay: ${delayMs}ms. Must be between 0 and 24 hours`);
    }

    const retryJobName = `${userId}_${targetId}_${type}_retry_${Date.now()}`;
    const jobData: AutoActionJobData = {
      userId,
      targetId,
      type,
    };

    const jobOptions = {
      delay: delayMs,
      removeOnComplete: 5, // Keep fewer completed retry jobs
      removeOnFail: 10, // Keep fewer failed retry jobs
      attempts: 1, // Don't retry the retry job to avoid infinite loops
      // Add job timeout to prevent stuck jobs
      jobId: retryJobName, // Ensure unique job ID
    };

    try {
      // Add retry job with timeout protection
      const addJobPromise = this.autoActionQueue.add(retryJobName, jobData, jobOptions);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Retry job creation timeout')), this.OPERATION_TIMEOUT)
      );

      await Promise.race([addJobPromise, timeoutPromise]);

      this.logger.log(`Successfully added retry job: ${retryJobName} with ${delayMs}ms delay`);
    } catch (error) {
      this.logger.error(`Failed to add retry job: ${retryJobName}`, {
        error: error.message,
        stack: error.stack,
        userId,
        targetId,
        type,
        delayMs,
      });
      throw new Error(`Failed to create retry job: ${error.message}`);
    }
  }

  /**
   * Remove an auto action job from the queue with comprehensive error handling
   */
  async removeAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<{ success: boolean; removedRepeatable: boolean; removedJobs: number; errors: string[] }> {
    // Input validation
    if (!userId || userId <= 0) {
      throw new Error('Invalid userId provided for job removal');
    }
    if (!targetId || typeof targetId !== 'string') {
      throw new Error('Invalid targetId provided for job removal');
    }
    if (!Object.values(AutoMode).includes(type)) {
      throw new Error(`Invalid auto mode type for job removal: ${type}`);
    }

    const jobName = this.generateJobName(userId, targetId, type);
    const result = {
      success: false,
      removedRepeatable: false,
      removedJobs: 0,
      errors: [] as string[],
    };

    try {
      // Step 1: Remove repeatable job pattern
      result.removedRepeatable = await this.removeRepeatableJobSafely(jobName);

      // Step 2: Remove individual jobs (waiting, delayed, active, retry jobs)
      result.removedJobs = await this.removeIndividualJobsSafely(jobName);

      // Step 3: Final cleanup - remove any remaining jobs
      await this.performFinalCleanup(jobName, result);

      result.success = result.removedRepeatable || result.removedJobs > 0;

      this.logger.log(
        `Job removal completed for ${jobName}: ` +
        `repeatable=${result.removedRepeatable}, individual=${result.removedJobs}, ` +
        `success=${result.success}, errors=${result.errors.length}`
      );

      return result;
    } catch (error) {
      const errorMsg = `Critical error removing job ${jobName}: ${error.message}`;
      this.logger.error(errorMsg, {
        error: error.message,
        stack: error.stack,
        userId,
        targetId,
        type,
      });
      result.errors.push(errorMsg);
      result.success = false;
      return result;
    }
  }

  /**
   * Safely remove repeatable job pattern with multiple strategies
   */
  private async removeRepeatableJobSafely(jobName: string): Promise<boolean> {
    try {
      // Strategy 1: Remove with known repeat options
      try {
        const removed = await this.autoActionQueue.removeRepeatable(jobName, {
          every: this.REPEAT_INTERVAL,
        });
        if (removed) {
          this.logger.log(`Successfully removed repeatable job: ${jobName}`);
          return true;
        }
      } catch (error) {
        this.logger.debug(`Strategy 1 failed for ${jobName}: ${error.message}`);
      }

      // Strategy 2: Find and remove by inspecting existing repeatable jobs
      try {
        const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
        const targetJob = repeatableJobs.find(job => job.name === jobName);

        if (targetJob) {
          const repeatOptions: any = {};

          if (typeof targetJob.every === 'number') {
            repeatOptions.every = targetJob.every;
          }
          if (targetJob.pattern) {
            repeatOptions.pattern = targetJob.pattern;
          }
          if (targetJob.tz) {
            repeatOptions.tz = targetJob.tz;
          }

          const removed = await this.autoActionQueue.removeRepeatable(
            targetJob.name,
            repeatOptions,
            targetJob.id || undefined
          );

          if (removed) {
            this.logger.log(`Successfully removed repeatable job via strategy 2: ${jobName}`);
            return true;
          }
        }
      } catch (error) {
        this.logger.debug(`Strategy 2 failed for ${jobName}: ${error.message}`);
      }

      return false;
    } catch (error) {
      this.logger.warn(`All repeatable job removal strategies failed for ${jobName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Safely remove individual jobs (waiting, delayed, active, retry jobs)
   */
  private async removeIndividualJobsSafely(jobName: string): Promise<number> {
    let removedCount = 0;

    try {
      // Get all job states that might contain our jobs
      const jobStates = ['waiting', 'delayed', 'active', 'completed', 'failed'] as const;

      for (const state of jobStates) {
        try {
          const jobs = await this.autoActionQueue.getJobs([state], 0, 100); // Limit to prevent memory issues

          for (const job of jobs) {
            try {
              const shouldRemove =
                job.name === jobName ||
                job.name.startsWith(`${jobName}_retry_`);

              if (shouldRemove) {
                await job.remove();
                removedCount++;
                this.logger.debug(`Removed ${state} job: ${job.name}`);
              }
            } catch (jobError) {
              // Some jobs might be protected or already removed
              if (!jobError.message?.includes('belongs to a job scheduler')) {
                this.logger.debug(`Failed to remove job ${job.name}: ${jobError.message}`);
              }
            }
          }
        } catch (stateError) {
          this.logger.debug(`Failed to get ${state} jobs: ${stateError.message}`);
        }
      }

      if (removedCount > 0) {
        this.logger.log(`Removed ${removedCount} individual jobs for pattern: ${jobName}`);
      }

      return removedCount;
    } catch (error) {
      this.logger.warn(`Error removing individual jobs for ${jobName}: ${error.message}`);
      return removedCount;
    }
  }

  /**
   * Perform final cleanup and validation
   */
  private async performFinalCleanup(
    jobName: string,
    result: { success: boolean; removedRepeatable: boolean; removedJobs: number; errors: string[] }
  ): Promise<void> {
    try {
      // Wait a bit for queue operations to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Verify removal was successful
      const stillExists = await this.jobExists(
        parseInt(jobName.split('_')[0]),
        jobName.split('_')[1],
        jobName.split('_')[2] as AutoMode
      );

      if (stillExists && result.success) {
        this.logger.warn(`Job ${jobName} still exists after removal attempt`);
        result.errors.push(`Job still exists after removal`);
      }
    } catch (error) {
      this.logger.debug(`Final cleanup check failed for ${jobName}: ${error.message}`);
    }
  }

  /**
   * Get all active auto action jobs with comprehensive error handling
   */
  async getActiveJobs(): Promise<Array<{ userId: number; targetId: string; type: AutoMode }>> {
    try {
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Get active jobs timeout')), this.OPERATION_TIMEOUT)
      );

      const getJobsPromise = this.autoActionQueue.getRepeatableJobs();
      const repeatableJobs = await Promise.race([getJobsPromise, timeoutPromise]);

      const activeJobs: Array<{ userId: number; targetId: string; type: AutoMode }> = [];

      for (const job of repeatableJobs) {
        try {
          const parts = job.name.split('_');
          if (parts.length >= 3) {
            const userId = parseInt(parts[0]);
            const targetId = parts[1];
            const type = parts[2] as AutoMode;

            // Validate parsed data
            if (userId > 0 && targetId && Object.values(AutoMode).includes(type)) {
              activeJobs.push({ userId, targetId, type });
            } else {
              this.logger.warn(`Invalid job data parsed from ${job.name}: userId=${userId}, targetId=${targetId}, type=${type}`);
            }
          } else {
            this.logger.warn(`Invalid job name format: ${job.name}`);
          }
        } catch (parseError) {
          this.logger.warn(`Failed to parse job: ${job.name}`, parseError.message);
        }
      }

      this.logger.debug(`Retrieved ${activeJobs.length} active jobs`);
      return activeJobs;
    } catch (error) {
      this.logger.error('Failed to get active jobs', {
        error: error.message,
        stack: error.stack,
      });
      return [];
    }
  }

  /**
   * Get all active auto action jobs with user validation and comprehensive error handling
   * This method checks if the jobs match the user's current auto mode settings
   */
  async getActiveJobsWithValidation(userService: any): Promise<Array<{
    userId: number;
    targetId: string;
    type: AutoMode;
    isValid: boolean;
    userAutoMode?: AutoMode;
    userTargetId?: string;
  }>> {
    if (!userService) {
      this.logger.error('UserService is required for job validation');
      return [];
    }

    try {
      // Use timeout protection
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Job validation timeout')), this.OPERATION_TIMEOUT)
      );

      const validationPromise = this.performJobValidation(userService);
      const result = await Promise.race([validationPromise, timeoutPromise]);

      this.logger.debug(`Job validation completed: ${result.length} jobs processed`);
      return result;
    } catch (error) {
      this.logger.error('Failed to get active jobs with validation', {
        error: error.message,
        stack: error.stack,
      });
      return [];
    }
  }

  /**
   * Perform the actual job validation with batch processing
   */
  private async performJobValidation(userService: any): Promise<Array<{
    userId: number;
    targetId: string;
    type: AutoMode;
    isValid: boolean;
    userAutoMode?: AutoMode;
    userTargetId?: string;
  }>> {
    const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
    const jobsWithValidation: Array<{
      userId: number;
      targetId: string;
      type: AutoMode;
      isValid: boolean;
      userAutoMode?: AutoMode;
      userTargetId?: string;
    }> = [];

    // Process jobs in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < repeatableJobs.length; i += batchSize) {
      const batch = repeatableJobs.slice(i, i + batchSize);

      await Promise.all(batch.map(async (job) => {
        try {
          const parts = job.name.split('_');
          if (parts.length < 3) {
            this.logger.warn(`Invalid job name format: ${job.name}`);
            return;
          }

          const [userIdStr, targetId, typeStr] = parts;
          const parsedUserId = parseInt(userIdStr);
          const type = typeStr as AutoMode;

          // Validate parsed data
          if (!parsedUserId || parsedUserId <= 0) {
            this.logger.warn(`Invalid userId in job: ${job.name}`);
            return;
          }
          if (!Object.values(AutoMode).includes(type)) {
            this.logger.warn(`Invalid auto mode in job: ${job.name}`);
            return;
          }

          // Get user's current auto mode settings with error handling
          let user: any = null;
          try {
            user = await userService.findOne(parsedUserId);
          } catch (userError) {
            this.logger.warn(`Failed to fetch user ${parsedUserId}: ${userError.message}`);
          }

          const isValid = user &&
            user.activeAutoMode === type &&
            user.autoTargetId === targetId;

          jobsWithValidation.push({
            userId: parsedUserId,
            targetId,
            type,
            isValid: !!isValid,
            userAutoMode: user?.activeAutoMode,
            userTargetId: user?.autoTargetId,
          });

          // Log inconsistent jobs for monitoring
          if (!isValid) {
            this.logger.warn(
              `Inconsistent job detected: ${job.name}. ` +
              `User auto mode: ${user?.activeAutoMode}, target: ${user?.autoTargetId}`
            );
          }
        } catch (jobError) {
          this.logger.error(`Error processing job ${job.name}: ${jobError.message}`);
        }
      }));
    }

    return jobsWithValidation;
  }

  /**
   * Clean up inconsistent jobs that don't match user's current auto mode settings with comprehensive error handling
   */
  async cleanupInconsistentJobs(userService: any): Promise<{
    totalJobs: number;
    inconsistentJobs: number;
    cleanedJobs: number;
    errors: string[];
    duration: number;
  }> {
    const startTime = Date.now();
    const result = {
      totalJobs: 0,
      inconsistentJobs: 0,
      cleanedJobs: 0,
      errors: [] as string[],
      duration: 0,
    };

    if (!userService) {
      const error = 'UserService is required for cleanup operation';
      this.logger.error(error);
      result.errors.push(error);
      return result;
    }

    try {
      this.logger.log('Starting inconsistent jobs cleanup...');

      // Use timeout protection for the entire cleanup operation
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Cleanup operation timeout')), 5 * 60 * 1000) // 5 minutes
      );

      const cleanupPromise = this.performInconsistentJobsCleanup(userService, result);
      await Promise.race([cleanupPromise, timeoutPromise]);

      result.duration = Date.now() - startTime;

      this.logger.log(
        `Cleanup completed in ${result.duration}ms: ` +
        `${result.cleanedJobs}/${result.inconsistentJobs} inconsistent jobs removed ` +
        `(${result.errors.length} errors)`
      );

      return result;
    } catch (error) {
      result.duration = Date.now() - startTime;
      const errorMsg = `Cleanup operation failed after ${result.duration}ms: ${error.message}`;
      this.logger.error(errorMsg, {
        error: error.message,
        stack: error.stack,
      });
      result.errors.push(errorMsg);
      return result;
    }
  }

  /**
   * Perform the actual cleanup with batch processing and error recovery
   */
  private async performInconsistentJobsCleanup(
    userService: any,
    result: { totalJobs: number; inconsistentJobs: number; cleanedJobs: number; errors: string[] }
  ): Promise<void> {
    const jobsWithValidation = await this.getActiveJobsWithValidation(userService);
    result.totalJobs = jobsWithValidation.length;

    const inconsistentJobs = jobsWithValidation.filter(job => !job.isValid);
    result.inconsistentJobs = inconsistentJobs.length;

    this.logger.log(
      `Found ${result.inconsistentJobs} inconsistent jobs out of ${result.totalJobs} total jobs`
    );

    if (inconsistentJobs.length === 0) {
      this.logger.log('No inconsistent jobs found, cleanup not needed');
      return;
    }

    // Process cleanup in batches to avoid overwhelming the system
    const batchSize = 5;
    for (let i = 0; i < inconsistentJobs.length; i += batchSize) {
      const batch = inconsistentJobs.slice(i, i + batchSize);

      await Promise.all(batch.map(async (job) => {
        try {
          const removalResult = await this.removeAutoActionJob(job.userId, job.targetId, job.type);

          if (removalResult.success) {
            result.cleanedJobs++;
            this.logger.log(
              `Successfully cleaned up inconsistent job: ${job.userId}_${job.targetId}_${job.type} ` +
              `(user mode: ${job.userAutoMode}, user target: ${job.userTargetId})`
            );
          } else {
            const errorMsg = `Partial cleanup for job ${job.userId}_${job.targetId}_${job.type}: ${removalResult.errors.join(', ')}`;
            result.errors.push(errorMsg);
            this.logger.warn(errorMsg);
          }
        } catch (error) {
          const errorMsg = `Failed to clean up job ${job.userId}_${job.targetId}_${job.type}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }));

      // Small delay between batches to prevent overwhelming the system
      if (i + batchSize < inconsistentJobs.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  /**
   * Check if a specific auto action job exists with comprehensive validation
   */
  async jobExists(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<boolean> {
    // Input validation
    if (!userId || userId <= 0) {
      this.logger.warn('Invalid userId provided for job existence check');
      return false;
    }
    if (!targetId || typeof targetId !== 'string') {
      this.logger.warn('Invalid targetId provided for job existence check');
      return false;
    }
    if (!Object.values(AutoMode).includes(type)) {
      this.logger.warn(`Invalid auto mode type for job existence check: ${type}`);
      return false;
    }

    const jobName = this.generateJobName(userId, targetId, type);

    try {
      // Use timeout protection for all operations
      const checkPromise = this.performJobExistenceCheck(jobName);
      const timeoutPromise = new Promise<boolean>((_, reject) =>
        setTimeout(() => reject(new Error('Job existence check timeout')), this.OPERATION_TIMEOUT)
      );

      const exists = await Promise.race([checkPromise, timeoutPromise]);

      this.logger.debug(`Job existence check for ${jobName}: ${exists}`);
      return exists;
    } catch (error) {
      this.logger.error(`Failed to check if job exists: ${jobName}`, {
        error: error.message,
        stack: error.stack,
        userId,
        targetId,
        type,
      });
      return false; // Assume job doesn't exist on error to prevent duplicates
    }
  }

  /**
   * Perform the actual job existence check with multiple strategies
   */
  private async performJobExistenceCheck(jobName: string): Promise<boolean> {
    try {
      // Strategy 1: Check repeatable jobs
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      const repeatableExists = repeatableJobs.some(job => job.name === jobName);

      if (repeatableExists) {
        this.logger.debug(`Found existing repeatable job: ${jobName}`);
        return true;
      }

      // Strategy 2: Check pending/active jobs
      const jobStates = ['waiting', 'delayed', 'active'] as const;
      for (const state of jobStates) {
        try {
          const jobs = await this.autoActionQueue.getJobs([state], 0, 50); // Limit for performance
          const stateExists = jobs.some(job => job.name === jobName);

          if (stateExists) {
            this.logger.debug(`Found existing ${state} job: ${jobName}`);
            return true;
          }
        } catch (stateError) {
          this.logger.debug(`Failed to check ${state} jobs: ${stateError.message}`);
        }
      }

      // Strategy 3: Double-check with delay to handle race conditions
      await new Promise(resolve => setTimeout(resolve, 100));

      const finalRepeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      const finalExists = finalRepeatableJobs.some(job => job.name === jobName);

      if (finalExists) {
        this.logger.debug(`Found existing repeatable job on final check: ${jobName}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.warn(`Error during job existence check for ${jobName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Force remove a job by trying all possible removal methods with comprehensive error handling
   * This is a more aggressive approach for stuck jobs
   */
  async forceRemoveAutoActionJob(
    userId: number,
    targetId: string,
    type: AutoMode,
  ): Promise<{
    success: boolean;
    methods: string[];
    errors: string[];
    duration: number;
  }> {
    const startTime = Date.now();

    // Input validation
    if (!userId || userId <= 0) {
      throw new Error('Invalid userId provided for force removal');
    }
    if (!targetId || typeof targetId !== 'string') {
      throw new Error('Invalid targetId provided for force removal');
    }
    if (!Object.values(AutoMode).includes(type)) {
      throw new Error(`Invalid auto mode type for force removal: ${type}`);
    }

    const jobName = this.generateJobName(userId, targetId, type);
    const result = {
      success: false,
      methods: [] as string[],
      errors: [] as string[],
      duration: 0,
    };

    this.logger.log(`Starting force removal for job: ${jobName}`);

    try {
      // Use timeout protection for the entire force removal operation
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Force removal timeout')), 2 * 60 * 1000) // 2 minutes
      );

      const forceRemovalPromise = this.performForceRemoval(jobName, userId, targetId, type, result);
      await Promise.race([forceRemovalPromise, timeoutPromise]);

      result.duration = Date.now() - startTime;

      this.logger.log(
        `Force removal completed for ${jobName} in ${result.duration}ms: ` +
        `success=${result.success}, methods=[${result.methods.join(', ')}], ` +
        `errors=${result.errors.length}`
      );

      return result;
    } catch (error) {
      result.duration = Date.now() - startTime;
      const errorMsg = `Force removal failed for ${jobName} after ${result.duration}ms: ${error.message}`;
      this.logger.error(errorMsg, {
        error: error.message,
        stack: error.stack,
        userId,
        targetId,
        type,
      });
      result.errors.push(errorMsg);
      return result;
    }
  }

  /**
   * Perform the actual force removal with multiple strategies
   */
  private async performForceRemoval(
    jobName: string,
    userId: number,
    targetId: string,
    type: AutoMode,
    result: { success: boolean; methods: string[]; errors: string[] }
  ): Promise<void> {
    // Strategy 1: Regular removal (most reliable)
    try {
      const removalResult = await this.removeAutoActionJob(userId, targetId, type);
      if (removalResult.success) {
        result.methods.push('regular_removal');
        result.success = true;
        return;
      } else {
        result.errors.push(`Regular removal partial: ${removalResult.errors.join(', ')}`);
      }
    } catch (error) {
      const errorMsg = `Regular removal failed: ${error.message}`;
      this.logger.warn(errorMsg);
      result.errors.push(errorMsg);
    }

    // Strategy 2: Aggressive repeatable job removal
    try {
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      let removedRepeatable = false;

      for (const job of repeatableJobs) {
        if (job.name === jobName) {
          try {
            // Try multiple removal patterns
            const patterns = [
              { every: this.REPEAT_INTERVAL },
              { pattern: job.pattern || '*/30 * * * *' },
              { every: 30 * 60 * 1000 }, // Fallback pattern
            ];

            for (const pattern of patterns) {
              try {
                const removed = await this.autoActionQueue.removeRepeatable(
                  job.name,
                  pattern,
                  job.id || undefined
                );
                if (removed) {
                  removedRepeatable = true;
                  break;
                }
              } catch (patternError) {
                // Try next pattern
              }
            }
          } catch (jobError) {
            result.errors.push(`Failed to remove repeatable job ${job.name}: ${jobError.message}`);
          }
        }
      }

      if (removedRepeatable) {
        result.methods.push('aggressive_repeatable_removal');
        result.success = true;
      }
    } catch (error) {
      const errorMsg = `Aggressive repeatable removal failed: ${error.message}`;
      this.logger.warn(errorMsg);
      result.errors.push(errorMsg);
    }

    // Strategy 3: Brute force individual job removal
    try {
      const jobStates = ['waiting', 'delayed', 'active', 'completed', 'failed'] as const;
      let totalRemoved = 0;

      for (const state of jobStates) {
        try {
          const jobs = await this.autoActionQueue.getJobs([state], 0, 200); // Increased limit

          for (const job of jobs) {
            const shouldRemove =
              job.name === jobName ||
              job.name.startsWith(`${jobName}_retry_`) ||
              job.name.includes(`${userId}_${targetId}`); // More aggressive matching

            if (shouldRemove) {
              try {
                await job.remove();
                totalRemoved++;
              } catch (jobError) {
                // Individual job removal might fail, continue with others
                if (!jobError.message?.includes('belongs to a job scheduler')) {
                  result.errors.push(`Failed to remove ${state} job ${job.name}: ${jobError.message}`);
                }
              }
            }
          }
        } catch (stateError) {
          result.errors.push(`Failed to get ${state} jobs: ${stateError.message}`);
        }
      }

      if (totalRemoved > 0) {
        result.methods.push(`brute_force_removal_${totalRemoved}`);
        result.success = true;
      }
    } catch (error) {
      const errorMsg = `Brute force removal failed: ${error.message}`;
      this.logger.warn(errorMsg);
      result.errors.push(errorMsg);
    }

    // Final verification
    try {
      const stillExists = await this.jobExists(userId, targetId, type);
      if (stillExists && result.success) {
        result.errors.push('Job still exists after force removal');
        this.logger.warn(`Job ${jobName} still exists after force removal attempts`);
      }
    } catch (verifyError) {
      result.errors.push(`Verification failed: ${verifyError.message}`);
    }
  }

  /**
   * Clean up all travel auto jobs (travel should not be repeating)
   */
  async cleanupTravelAutoJobs(): Promise<{
    totalTravelJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    const result = {
      totalTravelJobs: 0,
      removedJobs: 0,
      errors: [] as string[],
    };

    try {
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      const travelJobs = repeatableJobs.filter(job => job.name.endsWith('_travel'));
      result.totalTravelJobs = travelJobs.length;

      this.logger.log(`Found ${result.totalTravelJobs} travel auto jobs to clean up`);

      for (const job of travelJobs) {
        try {
          const [userId, targetId] = job.name.split('_');
          await this.removeAutoActionJob(parseInt(userId), targetId, AutoMode.TRAVEL);
          result.removedJobs++;
          this.logger.log(`Cleaned up travel auto job: ${job.name}`);
        } catch (error) {
          const errorMsg = `Failed to clean up travel job ${job.name}: ${error.message}`;
          result.errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(
        `Travel auto job cleanup completed: ${result.removedJobs}/${result.totalTravelJobs} jobs removed`
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to cleanup travel auto jobs', error);
      result.errors.push(`Cleanup failed: ${error.message}`);
      return result;
    }
  }

  /**
   * Get comprehensive queue statistics with error handling and performance monitoring
   */
  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    repeatable: number;
    paused: boolean;
    health: 'healthy' | 'degraded' | 'unhealthy';
    lastUpdated: string;
    errors: string[];
  }> {
    const stats = {
      waiting: 0,
      active: 0,
      completed: 0,
      failed: 0,
      delayed: 0,
      repeatable: 0,
      paused: false,
      health: 'healthy' as 'healthy' | 'degraded' | 'unhealthy',
      lastUpdated: new Date().toISOString(),
      errors: [] as string[],
    };

    try {
      // Use timeout protection for all operations
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Queue stats timeout')), this.OPERATION_TIMEOUT)
      );

      const statsPromise = this.gatherQueueStatistics();
      const result = await Promise.race([statsPromise, timeoutPromise]);

      Object.assign(stats, result);

      // Determine health status
      stats.health = this.determineQueueHealth(stats);

      this.logger.debug(`Queue stats retrieved: ${JSON.stringify(stats)}`);
      return stats;
    } catch (error) {
      const errorMsg = `Failed to get queue stats: ${error.message}`;
      this.logger.error(errorMsg, {
        error: error.message,
        stack: error.stack,
      });

      stats.errors.push(errorMsg);
      stats.health = 'unhealthy';
      return stats;
    }
  }

  /**
   * Gather queue statistics from various sources
   */
  private async gatherQueueStatistics(): Promise<Partial<typeof this.getQueueStats extends () => Promise<infer T> ? T : never>> {
    const stats: any = {};
    const errors: string[] = [];

    // Gather stats from different job states
    const jobStates = [
      { key: 'waiting', method: 'getWaiting' },
      { key: 'active', method: 'getActive' },
      { key: 'completed', method: 'getCompleted' },
      { key: 'failed', method: 'getFailed' },
      { key: 'delayed', method: 'getDelayed' },
    ] as const;

    for (const { key, method } of jobStates) {
      try {
        const jobs = await (this.autoActionQueue as any)[method]();
        stats[key] = Array.isArray(jobs) ? jobs.length : 0;
      } catch (error) {
        this.logger.warn(`Failed to get ${key} jobs: ${error.message}`);
        stats[key] = 0;
        errors.push(`Failed to get ${key} jobs: ${error.message}`);
      }
    }

    // Get repeatable jobs count
    try {
      const repeatableJobs = await this.autoActionQueue.getRepeatableJobs();
      stats.repeatable = repeatableJobs.length;
    } catch (error) {
      this.logger.warn(`Failed to get repeatable jobs: ${error.message}`);
      stats.repeatable = 0;
      errors.push(`Failed to get repeatable jobs: ${error.message}`);
    }

    // Check if queue is paused
    try {
      stats.paused = await this.autoActionQueue.isPaused();
    } catch (error) {
      this.logger.warn(`Failed to check if queue is paused: ${error.message}`);
      stats.paused = false;
      errors.push(`Failed to check pause status: ${error.message}`);
    }

    stats.errors = errors;
    return stats;
  }

  /**
   * Determine queue health based on statistics
   */
  private determineQueueHealth(stats: any): 'healthy' | 'degraded' | 'unhealthy' {
    // Unhealthy conditions
    if (stats.errors.length > 3) return 'unhealthy';
    if (stats.paused) return 'unhealthy';
    if (stats.failed > 100) return 'unhealthy';

    // Degraded conditions
    if (stats.errors.length > 0) return 'degraded';
    if (stats.active > this.MAX_CONCURRENT_JOBS) return 'degraded';
    if (stats.failed > 10) return 'degraded';

    return 'healthy';
  }

  /**
   * Health check method for monitoring systems
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: any;
    timestamp: string;
  }> {
    try {
      const stats = await this.getQueueStats();

      return {
        status: stats.health,
        details: {
          queueStats: stats,
          redisConnection: 'connected', // BullMQ handles connection internally
          lastCheck: stats.lastUpdated,
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
          redisConnection: 'unknown',
        },
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Graceful shutdown method
   */
  async gracefulShutdown(): Promise<void> {
    try {
      this.logger.log('Starting graceful shutdown of auto action queue...');

      // Wait for active jobs to complete (with timeout)
      const shutdownTimeout = 30000; // 30 seconds
      const startTime = Date.now();

      while (Date.now() - startTime < shutdownTimeout) {
        const stats = await this.getQueueStats();
        if (stats.active === 0) {
          break;
        }

        this.logger.log(`Waiting for ${stats.active} active jobs to complete...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Close the queue
      await this.autoActionQueue.close();

      this.logger.log('Auto action queue shutdown completed');
    } catch (error) {
      this.logger.error('Error during graceful shutdown', error);
      throw error;
    }
  }
}



